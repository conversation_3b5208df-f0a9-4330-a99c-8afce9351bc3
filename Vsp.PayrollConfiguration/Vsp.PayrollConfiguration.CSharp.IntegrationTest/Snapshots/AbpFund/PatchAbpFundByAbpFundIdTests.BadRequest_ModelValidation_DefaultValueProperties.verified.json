{"messages": [{"code": 0, "description": "", "exception": null, "id": null, "messageCode": "ModelStateValidationError", "messageType": 0, "properties": "{\"franchise\":\"The Franchise field is required.\",\"totalContribution\":\"The TotalContribution field is required.\",\"franchiseUpToAge40\":\"The FranchiseUpToAge40 field is required.\",\"franchiseUpToAge50\":\"The FranchiseUpToAge50 field is required.\",\"employmentContribution\":\"The EmploymentContribution field is required.\"}", "type": "BrokenBusinessRule"}], "resultObject": null, "success": false}