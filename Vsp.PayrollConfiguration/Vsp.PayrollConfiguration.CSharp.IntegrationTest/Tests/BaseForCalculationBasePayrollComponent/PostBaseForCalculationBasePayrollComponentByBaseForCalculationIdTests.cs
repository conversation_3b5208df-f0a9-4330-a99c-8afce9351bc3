using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationBasePayrollComponent;

[Collection(EntityNames.BaseForCalculationBasePayrollComponent)]
public class PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationBasePayrollComponent";
    protected override bool UseTransaction => true;
}