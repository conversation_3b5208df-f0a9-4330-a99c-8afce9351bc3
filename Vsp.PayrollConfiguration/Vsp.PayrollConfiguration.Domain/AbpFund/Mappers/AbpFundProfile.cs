using Vsp.PayrollConfiguration.Domain.AbpFund.Models;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Mappers;

internal class AbpFundProfile : Profile
{
    public AbpFundProfile()
    {
        #region GET

        CreateMap<Repository.Entities.AbpFund, AbpFundModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src.AbpFundId))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.CtAbpFund.Omschrijving))
            .ForMember(dst => dst.TotalContribution, opt => opt.MapFrom(src => src.TotalContribution))
            .ForMember(dst => dst.EmploymentContribution, opt => opt.MapFrom(src => src.EmploymentContribution))
            .ForMember(dst => dst.Franchise, opt => opt.MapFrom(src => src.Franchise))
            .ForMember(dst => dst.FranchiseUpToAge40, opt => opt.MapFrom(src => src.FranchiseUpToAge40))
            .ForMember(dst => dst.FranchiseUpToAge50, opt => opt.MapFrom(src => src.FranchiseUpToAge50))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Repository.Entities.AbpFund, AbpFundDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.AbpFundIdDefinedAtLevel))
            .ForMember(dst => dst.TotalContribution, opt => opt.MapFrom(src => src.TotalContributionDefinedAtLevel))
            .ForMember(dst => dst.EmploymentContribution, opt => opt.MapFrom(src => src.EmploymentContributionDefinedAtLevel))
            .ForMember(dst => dst.Franchise, opt => opt.MapFrom(src => src.FranchiseDefinedAtLevel))
            .ForMember(dst => dst.FranchiseUpToAge40, opt => opt.MapFrom(src => src.FranchiseUpToAge40DefinedAtLevel))
            .ForMember(dst => dst.FranchiseUpToAge50, opt => opt.MapFrom(src => src.FranchiseUpToAge50DefinedAtLevel));

        #endregion
        
        #region PATCH

        CreateMap<AbpFundPatchModel, Repository.Entities.AbpFund>()
            .ForMember(dst => dst.TotalContribution, opt => opt.MapFrom(src => src.TotalContribution))
            .ForMember(dst => dst.EmploymentContribution, opt => opt.MapFrom(src => src.EmploymentContribution))
            .ForMember(dst => dst.Franchise, opt => opt.MapFrom(src => src.Franchise))
            .ForMember(dst => dst.FranchiseUpToAge40, opt => opt.MapFrom(src => src.FranchiseUpToAge40))
            .ForMember(dst => dst.FranchiseUpToAge50, opt => opt.MapFrom(src => src.FranchiseUpToAge50))
            // Ignore the primary key properties as they are not needed for PATCH operations
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.AbpFundId, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.Ignore())
            // Ignore the DefinedAtLevel properties as they are not needed for PATCH operations
            .ForMember(dst => dst.AbpFundIdDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.TotalContributionDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.EmploymentContributionDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.FranchiseDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.FranchiseUpToAge40DefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.FranchiseUpToAge50DefinedAtLevel, opt => opt.Ignore())
            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.CtAbpFund, opt => opt.Ignore());

        CreateMap<Repository.Entities.AbpFund, ModelAbpFund>()
            .ForMember(dst => dst.TotalContribution, opt => opt.MapFrom(src => src.TotalContribution))
            .ForMember(dst => dst.EmploymentContribution, opt => opt.MapFrom(src => src.EmploymentContribution))
            .ForMember(dst => dst.Franchise, opt => opt.MapFrom(src => src.Franchise))
            .ForMember(dst => dst.FranchiseUpToAge40, opt => opt.MapFrom(src => src.FranchiseUpToAge40))
            .ForMember(dst => dst.FranchiseUpToAge50, opt => opt.MapFrom(src => src.FranchiseUpToAge50))
            // Mapping the primary key properties
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.AbpFundId, opt => opt.MapFrom(src => src.AbpFundId))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.PayrollPeriodId))
            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore());
        
        #endregion
    }
}
