using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Extensions;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Validators;

internal class PatchBaseForCalculationValidator : InitializableAbstractValidator<BaseForCalculationPatchModel, PatchBaseForCalculationValidationInfo>
{
    private const int HolidayAccrualComponentId = 182;
    private const int ADVAccrualComponentId = 184;

    private readonly ILoketContext loketContext;
    private readonly ICodeTableHelper codeTableHelper;


    public PatchBaseForCalculationValidator(ILoketContext loketContext, ICodeTableHelper codeTableHelper)
    {
        this.loketContext = loketContext;
        this.codeTableHelper = codeTableHelper;

        // Stage 1 validations: Code table validation rules
        // Rule 1
        RuleFor(x => x)
            .MustAsync(async (x, _) => await BeValidCodeTableAsync<CtBaseType>(x.BaseType, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_BaseType_Invalid)
            .WithMessage("baseType.key is invalid");
        // Rule 2
        RuleFor(x => x)
            .MustAsync(async (x, _) => await BeValidCodeTableAsync<CtEmployeeAgeType>(x.StartEmployeeAgeType, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_StartEmployeeAgeType_Invalid)
            .WithMessage("startEmployeeAgeType.key is invalid");
        // Rule 3
        RuleFor(x => x)
            .MustAsync(async (x, _) => await BeValidCodeTableAsync<CtEmployeeAgeType>(x.EndEmployeeAgeType, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_EndEmployeeAgeType_Invalid)
            .WithMessage("endEmployeeAgeType.key is invalid");
        // Rule 4
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidPayrollComponentAsync(x.ResultPayrollComponent.Key, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_Invalid)
            .WithMessage("resultPayrollComponent.key is invalid");
        // Rule 5
        RuleFor(x => x)
            .Must(x => BeValidPayrollPeriod(x.CalculationPayrollPeriod?.PeriodNumber, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_Invalid)
            .WithMessage("calculationPayrollPeriod.periodNumber is invalid");
        // Rule 6
        RuleFor(x => x)
            .Must(x => BeValidPayrollPeriod(x.ReferencePayrollPeriod?.PeriodNumber, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Invalid)
            .WithMessage("referencePayrollPeriod.periodNumber is invalid");
        // Rule 7
        RuleFor(x => x)
            .Must(x => BeValidPayrollPeriod(x.PayoutPayrollPeriod?.PeriodNumber, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Invalid)
            .WithMessage("payoutPayrollPeriod.periodNumber is invalid");
        // Rule 8
        RuleFor(x => x)
            .Must(x => BeValidPayrollPeriod(x.AccrualEndPayrollPeriod?.PeriodNumber, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Invalid)
            .WithMessage("accrualEndPayrollPeriod.periodNumber is invalid");
        // Rule 9
        RuleFor(x => x)
            .MustAsync(async (x, _) => await BeValidCodeTableAsync<CtPayslipType>(x.PayslipType, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PayslipType_Invalid)
            .WithMessage("payslipType.key is invalid");
        // Rule 10
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidPayrollComponentAsync(x.AdvancePayrollComponent?.Key, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_Invalid)
            .WithMessage("advancePayrollComponent.key is invalid");
        // Rule 11
        RuleFor(x => x)
            .Must(x => BeValidPayrollPeriod(x.AdvancePayrollPeriod?.PeriodNumber, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_Invalid)
            .WithMessage("advancePayrollPeriod.periodNumber is invalid");
        // Rule 12
        RuleFor(x => x)
            .MustAsync(async (x, token) => await BeValidPayrollComponentAsync(x.PeriodicReservationPayrollComponent?.Key, x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_Invalid)
            .WithMessage("periodicReservationPayrollComponent.key is invalid");
        // Rule 13
        RuleFor(x => x)
            .MustAsync(async (x, _) => await BeValidCodeTableAsync<CtMinimumMaximumType>(x.MinimumMaximumType, x))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_MinimumMaximumType_Invalid)
            .WithMessage("minimumMaximumType.key is invalid");

        // Stage 2 validations: Classic errors
        When(x => x.ShouldContinueValidation(), () =>
        {
            // Rule 1
            RuleFor(x => x)
                    .Must(x => !MatchConditionsForError(() => x.BaseType != null && x.FinancialReservationPercentage == 0 && x.PeriodicReservationPayrollComponent == null && !x.IsAutomaticCalculation!.Value, x))
                    .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_BaseType_1)
                    .WithMessage("For payroll tax return purposes, one must reserve either periodically or financially.");
            // Rule 2
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.StartEmployeeAge == 0 && x.StartEmployeeAgeType != null && x.StartEmployeeAgeType.Key != 4 && x.StartEmployeeAgeType.Key != 5 && x.StartEmployeeAgeType.Key != 6, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_1)
                .WithMessage("Start moment provided but no start employee age.");
            // Rule 3
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.StartEmployeeAge != 0 && x.StartEmployeeAgeType == null, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_2)
                .WithMessage("No start moment provided, but a start employee age is specified.");
            // Rule 4
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.StartEmployeeAgeType != null && (x.StartEmployeeAgeType.Key == 4 || x.StartEmployeeAgeType.Key == 5 || x.StartEmployeeAgeType.Key == 6) && x.StartEmployeeAge != 0, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_3)
                .WithMessage("Start employee age must be zero in case of an AOW-based start moment.");
            // Rule 5
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.EndEmployeeAge != 0 && x.StartEmployeeAge > x.EndEmployeeAge, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_4)
                .WithMessage("Start employee age may not be larger than end employee age.");
            // Rule 6
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.EndEmployeeAge == 0 && x.EndEmployeeAgeType != null && x.EndEmployeeAgeType.Key != 4 && x.EndEmployeeAgeType.Key != 5 && x.EndEmployeeAgeType.Key != 6, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_1)
                .WithMessage("End moment provided but no end employee age.");
            // Rule 7
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.EndEmployeeAge != 0 && x.EndEmployeeAgeType == null, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_2)
                .WithMessage("No end moment provided, but an end employee age is specified.");
            // Rule 8
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.EndEmployeeAgeType != null && (x.EndEmployeeAgeType.Key == 4 || x.EndEmployeeAgeType.Key == 5 || x.EndEmployeeAgeType.Key == 6) && x.EndEmployeeAge != 0, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_3)
                .WithMessage("End employee age must be zero in case of an AOW-based end moment.");
            // Rule 9
            RuleFor(x => x)
                .MustAsync(async (x, token) => !await MatchConditionsForErrorAsync(async () =>
                    await IsNetToGrossComponentAsync(x.ResultPayrollComponent.Key, token), x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_2)
                .WithMessage("Result payroll component may not be a net-to-gross component.");
            // Rule 10
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() =>
                    x.CalculationPayrollPeriod != null &&
                    (x.IsAutomaticCalculation!.Value ||
                     (!x.IsAutomaticCalculation.Value &&
                      !x.IsCumulativeCalculation!.Value &&
                      !x.IsPartTimeCalculation!.Value)), x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_1)
                .WithMessage("Calculation payroll period must be empty for automatic calculation and term calculation.");
            // Rule 11
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.CalculationPayrollPeriod != null && x.PayoutPayrollPeriod == null, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_2)
                .WithMessage("If calculation payroll period is filled, payment payroll period must also be filled.");
            // Rule 12
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.ReferencePayrollPeriod != null && !x.IsPartTimeCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_1)
                .WithMessage("Reference payroll period is only allowed when calculating via part-time.");
            // Rule 13
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.PayoutPayrollPeriod == null && x.AdvancePayrollPeriod != null, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_1)
                .WithMessage("Advance payroll period may only be specified in combination with the payout payroll period.");
            // Rule 14
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.PayoutPayrollPeriod == null && x.IsPayoutAtEndOfEmployment!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_2)
                .WithMessage("Payout at end of employment may only be specified in combination with the payout payroll period.");
            // Rule 15
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.PayoutPayrollPeriod != null && x.PayoutPayrollPeriod.PeriodNumber == x.AdvancePayrollPeriod?.PeriodNumber, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_3)
                .WithMessage("Advance payroll period may not be equal to payout payroll period.");
            //Rule 16
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() =>
                    x.PayoutPayrollPeriod != null &&
                    !(x.IsCumulativeCalculation!.Value || (!x.IsCumulativeCalculation.Value && !x.IsAutomaticCalculation!.Value && x.IsPartTimeCalculation!.Value)), x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_4)
                .WithMessage("Payout payroll period is only allowed with a cumulative base for calculation and with a term calculation via part-time.");
            // Rule 17
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AccrualEndPayrollPeriod != null && !x.IsPartTimeCalculation!.Value && !x.IsCumulativeCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_1)
                .WithMessage("End of accrual payroll period is only allowed when calculating via part-time or cumulative calculation.");
            // Rule 18
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AccrualEndPayrollPeriod != null && x.CalculationPayrollPeriod?.PeriodNumber == x.AccrualEndPayrollPeriod.PeriodNumber, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_2)
                .WithMessage("End of accrual payroll period may not be the same as the calculation payroll period.");
            // Rule 19
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AccrualEndPayrollPeriod == null && x.IsPayoutAtStartOfEmployment!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_IsPayoutAtStartOfEmployment_1)
                .WithMessage("Advance payment upon employment only applies in case of extrapolation.");
            // Rule 20
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() =>
                    x.IsPayoutAtEndOfEmployment!.Value &&
                    (x.IsAutomaticCalculation!.Value ||
                     (!x.IsAutomaticCalculation.Value &&
                      !x.IsPartTimeCalculation!.Value &&
                      !x.IsCumulativeCalculation!.Value)), x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_IsPayoutAtEndOfEmployment_1)
                .WithMessage("Payment upon termination of employment does not apply when calculating automatically or in a term calculation.");
            // Rule 21
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AdvancePayrollComponent != null && x.IsAutomaticCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_1)
                .WithMessage("Advance payroll component must not be filled in when calculating automatically.");
            // Rule 22
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AdvancePayrollComponent == null && x.AdvancePercentage != 0, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_3)
                .WithMessage("Advance percentage is filled in. Therefore, the advance payroll component must be known.");
            // Rule 23
            RuleFor(x => x)
                .MustAsync(async (x, token) => !await MatchConditionsForErrorAsync(async () =>
                    x.AdvancePayrollComponent != null && await IsNetToGrossComponentAsync(x.AdvancePayrollComponent.Key, token), x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_4)
                .WithMessage("Advance payroll component may not be a net-to-gross component.");
            // Rule 24
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AdvancePercentage == 0 && x.AdvancePayrollComponent != null, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePercentage_1)
                .WithMessage("Advance percentage must be greater than 0.");
            // Rule 25
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AdvancePayrollPeriod != null && x.AdvancePayrollComponent == null, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_1)
                .WithMessage("Advance payroll period is only allowed in combination with an advance.");
            // Rule 26
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.PeriodicReservationPayrollComponent != null && x.IsAutomaticCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_1)
                .WithMessage("Periodic reservation payroll component must not be filled in when calculating automatically.");
            // Rule 27
            RuleFor(x => x)
                .MustAsync(async (x, token) => !await MatchConditionsForErrorAsync(async () =>
                    x.PeriodicReservationPayrollComponent != null &&
                    await IsNetToGrossComponentAsync(x.PeriodicReservationPayrollComponent.Key, token), x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_3)
                .WithMessage("Periodic reservation payroll component may not be a net-to-gross component.");
            // Rule 28
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.PeriodicReservationPayrollComponent != null && !x.IsCumulativeCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_4)
                .WithMessage("Periodic reservation component is only allowed with a cumulative base for calculation.");
            // Rule 29
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.FinancialReservationPercentage > 0 && x.IsAutomaticCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_FinancialReservationPercentage_1)
                .WithMessage("Financial reservation percentage is not allowed with automatic calculation.");
            // Rule 30
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.FinancialMarkupPercentage != 0 && x.FinancialReservationPercentage == 0, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_1)
                .WithMessage("Financial reservation percentage must be greater than 0 if the financial markup percentage is not 0.");
            // Rule 31
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.FinancialMarkupPercentage > 0 && x.IsAutomaticCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_2)
                .WithMessage("Financial markup percentage is not allowed with automatic calculation.");
            // Rule 32
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.IsPartTimeCalculation!.Value && (x.IsCumulativeCalculation!.Value || x.IsAutomaticCalculation!.Value), x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_IsPartTimeCalculation_1)
                .WithMessage("Base for calculation is not a term calculation; part-time must be NO.");
            // Rule 33
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.IsAutomaticCalculation!.Value && x.IsCumulativeCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_1)
                .WithMessage("Cumulative and automatic calculation may not both be set to YES.");
            // Rule 34
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => !x.IsAutomaticCalculation!.Value && x.ResultPayrollComponent.Key == HolidayAccrualComponentId, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_2)
                .WithMessage("Calculation must be automatic for holiday accrual (component 182).");
            // Rule 35
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => !x.IsAutomaticCalculation!.Value && x.ResultPayrollComponent.Key == ADVAccrualComponentId, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_3)
                .WithMessage("Calculation must be automatic for ADV accrual (component 184).");
            // Rule 36
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() =>
                    !x.IsCumulativeCalculation!.Value &&
                    !x.IsPartTimeCalculation!.Value &&
                    !x.IsAutomaticCalculation!.Value, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_1)
                .WithMessage("You have chosen a term calculation: not cumulative, not via part-time, and not automatic calculation. However, this type of calculation is no longer supported.");
            // Rule 37
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() => x.AdvancePayrollComponent != null && x.ResultPayrollComponent.Key == x.AdvancePayrollComponent.Key, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_AdvancePayrollComponent_Equal)
                .WithMessage("Result payroll component is equal to advance payroll component. This is not allowed.");
            //Rule 38
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() =>
                    x.PeriodicReservationPayrollComponent != null &&
                    x.ResultPayrollComponent.Key == x.PeriodicReservationPayrollComponent.Key, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_PeriodicReservationPayrollComponent_Equal)
                .WithMessage("Result payroll component is equal to periodic reservation payroll component. This is not allowed.");
            // Rule 39
            RuleFor(x => x)
                .Must(x => !MatchConditionsForError(() =>
                    x.AdvancePayrollComponent != null &&
                    x.PeriodicReservationPayrollComponent != null &&
                    x.AdvancePayrollComponent.Key == x.PeriodicReservationPayrollComponent.Key, x))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_PeriodicReservationPayrollComponent_Equal)
                .WithMessage("Advance payroll component is equal to periodic reservation payroll component. This is not allowed.");
        });

        // Stage 3 validations: Warnings from Classic
        When(x => x.ShouldContinueValidation(), () =>
        {
            // Rule 1
            RuleFor(x => x)
                .Must(x => !(x.PayoutPayrollPeriod != null && x.CalculationPayrollPeriod != null && x.PayoutPayrollPeriod.PeriodNumber < x.CalculationPayrollPeriod.PeriodNumber))
                .WithSeverity(Severity.Warning)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Warning_1)
                .WithMessage("Note! The payout payroll period lies in the NEXT year.");
            // Rule 2
            RuleFor(x => x)
                .Must(x => !(x.AccrualEndPayrollPeriod != null && x.CalculationPayrollPeriod != null && x.AccrualEndPayrollPeriod.PeriodNumber < x.CalculationPayrollPeriod.PeriodNumber))
                .WithSeverity(Severity.Warning)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_1)
                .WithMessage("Note! The accrual end payroll period lies in the NEXT year.");
            // Rule 3
            RuleFor(x => x)
                .Must(x => !(x.AccrualEndPayrollPeriod != null))
                .WithSeverity(Severity.Warning)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_2)
                .WithMessage("Note! The base for calculation will be extrapolated.");
            // Rule 4
            RuleFor(x => x)
                .Must(x => !(x.ReferencePayrollPeriod != null && x.CalculationPayrollPeriod != null && x.ReferencePayrollPeriod.PeriodNumber > x.CalculationPayrollPeriod.PeriodNumber))
                .WithSeverity(Severity.Warning)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_1)
                .WithMessage("Note! The reference payroll period lies in the PREVIOUS year.");
            // Rule 5
            RuleFor(x => x)
                .Must(x => !(x.ReferencePayrollPeriod == null && x.IsPartTimeCalculation!.Value))
                .WithSeverity(Severity.Warning)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_2)
                .WithMessage("Note! The reference payroll period will be set to the calculation payroll period.");
        });
    }

    private static bool MatchConditionsForError(Func<bool> conditionsForError, BaseForCalculationPatchModel patchModel)
    {
        var isError = conditionsForError();
        UpdateValidationContinuation(patchModel, !isError);
        return isError;
    }

    private static async Task<bool> MatchConditionsForErrorAsync(Func<Task<bool>> conditionsForError, BaseForCalculationPatchModel patchModel)
    {
        var isError = await conditionsForError();
        UpdateValidationContinuation(patchModel, !isError);
        return isError;
    }

    private static void UpdateValidationContinuation(IPatchModel patchModel, bool shouldContinue)
        => patchModel.SetValidationContinuation(patchModel.ShouldContinueValidation() && shouldContinue);

    private async Task<bool> BeValidCodeTableAsync<TCodeTable>(KeyModel? value, IPatchModel patchModel)
        where TCodeTable : CodeTable
    {
        if (value == null) return true;
        var isValid = (await this.codeTableHelper.GetOptions<TCodeTable>()).Any(x => x.Key == value.Key);
        UpdateValidationContinuation(patchModel, isValid);
        return isValid;
    }

    private async Task<bool> BeValidPayrollComponentAsync(int? componentId, BaseForCalculationPatchModel patchModel, CancellationToken token)
    {
        if (this.ValidationInformation.BaseForCalculation.Year == null) return true;
        if (componentId == null) return true;
        var oldEntity = this.ValidationInformation.BaseForCalculation;
        var isValid = await loketContext.Set<Component>()
            .AsNoTracking()
            .AnyAsync(x =>
                x.InheritanceLevelId == oldEntity.InheritanceLevelId &&
                x.YearId == oldEntity.YearId &&
                x.ComponentId == componentId, token);
        UpdateValidationContinuation(patchModel, isValid);
        return isValid;
    }

    private bool BeValidPayrollPeriod(int? period, BaseForCalculationPatchModel patchModel)
    {
        if (this.ValidationInformation.BaseForCalculation.Year == null) return true;
        if (period == null) return true;
        var payrollPeriodType = this.ValidationInformation.BaseForCalculation.Year.PayrollPeriodType;
        var isValid = payrollPeriodType switch
        {
            (int)PayrollPeriodType.Month => period >= 1 && period <= 12,
            (int)PayrollPeriodType.FourWeeks => period >= 1 && period <= 13,
            (int)PayrollPeriodType.Week => period >= 1 && period <= 52,
            _ => throw new ArgumentOutOfRangeException(nameof(payrollPeriodType),
                $"Invalid payroll period type: {payrollPeriodType}. Expected 1, 3, or 4.")
        };
        UpdateValidationContinuation(patchModel, isValid);
        return isValid;
    }

    private async Task<bool> IsNetToGrossComponentAsync(int? componentId, CancellationToken token)
    {
        if (componentId == null) return false;

        var oldEntity = this.ValidationInformation.BaseForCalculation;
        return await loketContext.Set<Component>()
            .AsNoTracking()
            .Where(x =>
                x.InheritanceLevelId == oldEntity.InheritanceLevelId &&
                x.YearId == oldEntity.YearId &&
                x.ComponentId == componentId &&
                x.IsNetToGross == 1)
            .AnyAsync(token);
    }

    protected override async Task<PatchBaseForCalculationValidationInfo> Initialize(BaseForCalculationPatchModel model)
        => new() { BaseForCalculation = await GetCurrentBaseForCalculationModel(model) };

    private async Task<Repository.Entities.BaseForCalculation> GetCurrentBaseForCalculationModel(BaseForCalculationPatchModel model)
    {
        var existingBaseForCalculation = await this.loketContext.Set<Repository.Entities.BaseForCalculation>()
            .Include(bfc => bfc.Year)
            .AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.BaseForCalculation>(model.Id))
            .SingleOrDefaultAsync();

        if (existingBaseForCalculation != null)
        {
            return existingBaseForCalculation;
        }

        if (model is not BaseForCalculationPostModel postModel)
        {
            throw new InvalidOperationException("Unable to get current base for calculation model. The model is not a post model and no existing entity was found.");
        }

        // Instantiate a new, partial, BaseForCalculation entity based on the post model. Needed for validations that require PatchBaseForCalculationValidationInfo.

        var newBaseForCalculation = new Repository.Entities.BaseForCalculation
        {
            InheritanceLevelId = await this.loketContext.Set<Repository.Entities.Base.InheritanceLevel>()
                .Where(x => x.Id == postModel.InheritanceLevelId)
                .Select(x => x.InheritanceLevelId)
                .SingleAsync(),
            YearId = postModel.Year!.Value,
            BaseForCalculationId = postModel.Key!.Value,
            PayrollPeriodId = postModel.StartPayrollPeriod.PeriodNumber!.Value,
        };

        // Get the Year entity from the database and set it on the entity
        var year = await this.loketContext.Set<Repository.Entities.Year>().AsNoTracking()
            .Where(y => y.YearId == newBaseForCalculation.YearId && y.InheritanceLevelId == newBaseForCalculation.InheritanceLevelId)
            .SingleOrDefaultAsync();
        if (year != null) newBaseForCalculation.Year = year;

        return newBaseForCalculation;
    }
}

public class PatchBaseForCalculationValidationInfo
{
    public Repository.Entities.BaseForCalculation BaseForCalculation { get; set; } = null!;
}
