using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;

public class BaseForCalculationBasePayrollComponentPostModel : BaseForCalculationBasePayrollComponentPatchModel, IPayrollPeriodPostModel
{
    [JsonIgnore]
    public Guid InheritanceLevelId { get; set; }

    [Required]
    public int? Year { get; set; }

    [Required]
    public KeyModel BaseForCalculation { get; set; } = null!;

    [Required]
    public KeyModel PayrollComponent { get; set; } = null!;

    [Required]
    public PayrollPeriodNumberModel StartPayrollPeriod { get; set; } = null!;
}