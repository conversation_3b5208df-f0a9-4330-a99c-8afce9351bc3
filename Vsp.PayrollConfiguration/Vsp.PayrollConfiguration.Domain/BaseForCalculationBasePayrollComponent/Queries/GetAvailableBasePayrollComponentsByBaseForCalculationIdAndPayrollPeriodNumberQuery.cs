using AutoMapper.QueryableExtensions;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Queries;

internal class GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery(
    ILoketContext context, 
    IMapper mapper):IGetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery
{
    private readonly ILoketContext context = context;
    private readonly IMapper mapper = mapper;

    public async Task<ListOperationResult<PayrollComponentMinimizedModel>> Execute(Guid baseForCalculationId, int payrollPeriodNumber)
    {
        var baseForCalculation = new Repository.Entities.BaseForCalculation() {Id = baseForCalculationId};
        GeneratedIdHelper.GenerateIdKeys(baseForCalculation);
        
        var componentsAddedToBaseForCalculationQuery 
            = this.context.Set<Repository.Entities.BaseForCalculationBasePayrollComponent>()
                .Where(bfcbpc => 
                    bfcbpc.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
                    bfcbpc.YearId == baseForCalculation.YearId &&
                    bfcbpc.BaseForCalculationId == baseForCalculation.BaseForCalculationId);

        var query = this.context.Set<Component>()
                         .Where(c => c.InheritanceLevelId == baseForCalculation.InheritanceLevelId && c.YearId == baseForCalculation.YearId)
                         .GroupJoin( componentsAddedToBaseForCalculationQuery, 
                             c => c.ComponentId, 
                             bfcbpc => bfcbpc.ComponentId,
                             (c, bfcbpc) => new { c, bfcbpc })
                         .SelectMany(
                             group => group.bfcbpc.DefaultIfEmpty(),
                             (group, bfcbpc) => new { group.c, bfcbpc })
                         .Where(x => 
                             (payrollPeriodNumber == 1 && x.bfcbpc == null) ||
                             (payrollPeriodNumber > 1 && 
                              x.bfcbpc != null &&
                              this.context.Set<Repository.Entities.BaseForCalculationBasePayrollComponent>()
                                  .Count(bfcbpc =>
                                      bfcbpc.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
                                      bfcbpc.YearId == baseForCalculation.YearId &&
                                      bfcbpc.BaseForCalculationId == baseForCalculation.BaseForCalculationId &&
                                      bfcbpc.ComponentId == x.c.ComponentId &&
                                      (bfcbpc.PayrollPeriodId == 1 || bfcbpc.PayrollPeriodId == payrollPeriodNumber)) == 1 && // The component is defined only in one period...
                              this.context.Set<Repository.Entities.BaseForCalculationBasePayrollComponent>()
                                  .Any(bfcbpc =>
                                      bfcbpc.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
                                      bfcbpc.YearId == baseForCalculation.YearId &&
                                      bfcbpc.BaseForCalculationId == baseForCalculation.BaseForCalculationId &&
                                      bfcbpc.ComponentId == x.c.ComponentId &&
                                      bfcbpc.PayrollPeriodId == 1) // ... and that period is period 1. 
                              ))
                         .Select(x => x.c)
                         .ProjectTo<PayrollComponentMinimizedModel>(this.mapper.ConfigurationProvider);
        
        var result = await query.ToArrayAsync();
        
        return new ListOperationResult<PayrollComponentMinimizedModel>(result);
    }
}