using System.Net;
using Vsp.PayrollConfiguration.BaseForCalculation.Constants;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.Infrastructure.Attributes;
using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.BaseForCalculation.Controllers;

/// <summary>
/// Base For Calculation related endpoints (NL: Grondslag)
/// </summary>
[Tags("Base For Calculation")]
[Authorize]
[ApiController]
public class BaseForCalculationController(IResultHandler resultHandler, IBaseForCalculationService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IBaseForCalculationService service = service;

    /// <summary>
    /// List of bases for calculation
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetBasesForCalculationByCollectiveLaborAgreementYearId</c>
    ///    - <c>GetBasesForCalculationByWageModelYearId</c>
    ///    - <c>GetBasesForCalculationByPayrollAdministrationYearId</c><br/>
    /// Retrieves the list of defined base for calculation on the given year of an inheritance level (CLA vs WM vs PA).
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        "827efae2-5c08-49f0-a107-7071fbb1e0d2", // GetBasesForCalculationByCollectiveLaborAgreementYearId
        "b1d10ea8-998b-42f4-b807-8feee777393c", // GetBasesForCalculationByWageModelYearId
        "2233b446-cff4-4958-930e-e00d027db4db")] // GetBasesForCalculationByPayrollAdministrationYearId
    public async Task<ActionResult<ListResult<BaseForCalculationModel>>> GetBasesForCalculationByYearIdAsync([FromRoute] Guid yearId)
    {
        var result = await this.service.GetBasesForCalculationByYearIdAsync(yearId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Add a new base for calculation
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PostBaseForCalculationByCollectiveLaborAgreementId</c>
    ///    - <c>PostBaseForCalculationByWageModelId</c>
    ///    - <c>PostBaseForCalculationByPayrollAdministrationId</c><br/>
    /// Adds a new base for calculation on the given inheritance level (CLA vs WM vs PA).
    /// <br/>**Validation errors**:
    ///    - <c>API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist</c> Payroll period does not exist on current inheritance level.
    ///    - <c>API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist</c> Cannot add data for a later payroll period in this year, because there is no data for the first payroll period yet.
    ///    - <c>API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel</c> Entity already exists on current inheritance level.
    ///    - <c>API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel</c> Entity already exists on parent inheritance level.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_Post_Key_ReservedCLA</c> Base for calculations 16-20 are reserved for the CLA level.
    /// <br/>**Validation warnings**:
    ///    - <c>API_PayrollConfiguration_Insert_Entity_NotAddedToFutureYear</c> Failed to automatically add entity to future year(s) as well. See properties for details.
    /// </remarks>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Post))]
    [HttpPost]
    [Route(BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync)]
    [AuthorizeInheritanceLevelEntity<InheritanceLevelAuthorizationModel>(
        "3a6cc614-274a-4c08-babd-e6ce6c4d5721", // PostBaseForCalculationByCollectiveLaborAgreementId
        "93b22aa4-c8bd-46a2-a266-ef1c8ab24aba", // PostBaseForCalculationByWageModelId
        "60243141-4f97-40b2-9918-737592f0bf5b")] // PostBaseForCalculationByPayrollAdministrationId
    public async Task<ActionResult<DetailResult<BaseForCalculationModel>>> PostBaseForCalculationByInheritanceLevelIdAsync(
        [FromQuery][ExactlyOneIdRequired] InheritanceLevelQuerystringParams querystringParams,
        [Required(ErrorMessage = "POST payload is required.")]
        BaseForCalculationPostModel postModel)
    {
        var result = await this.service.PostBaseForCalculationByInheritanceLevelIdAsync(querystringParams.GetId(), postModel);
        return this.resultHandler.ToTypedActionResult(result, (int)HttpStatusCode.Created);
    }

    /// <summary>
    /// Edit a base for calculation
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PatchBaseForCalculationByCollectiveLaborAgreementBaseForCalculationId</c>
    ///    - <c>PatchBaseForCalculationByWageModelBaseForCalculationId</c>
    ///    - <c>PatchBaseForCalculationByPayrollAdministrationBaseForCalculationId</c><br/>
    /// Modifies an existing base for calculation with the given id – defined on an inheritance level (CLA vs WM vs PA).
    /// <br/>**Validation errors**:
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_BaseType_Invalid</c> baseType.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_StartEmployeeAgeType_Invalid</c> startEmployeeAgeType.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_EndEmployeeAgeType_Invalid</c> endEmployeeAgeType.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_Invalid</c> resultPayrollComponent.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_Invalid</c> calculationPayrollPeriod.periodNumber is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Invalid</c> referencePayrollPeriod.periodNumber is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Invalid</c> payoutPayrollPeriod.periodNumber is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Invalid</c> accrualEndPayrollPeriod.periodNumber is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PayslipType_Invalid</c> payslipType.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_Invalid</c> advancePayrollComponent.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_Invalid</c> advancePayrollPeriod.periodNumber is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_Invalid</c> periodicReservationPayrollComponent.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_MinimumMaximumType_Invalid</c> minimumMaximumType.key is invalid
    ///
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_BaseType_1</c> For payroll tax return purposes, one must reserve either periodically or financially.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_1</c> Start moment provided but no start employee age.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_2</c> No start moment provided, but a start employee age is specified.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_3</c> Start employee age must be zero in case of an AOW-based start moment.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_4</c> Start employee age may not  be larger than end employee age.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_1</c> End moment provided but no end employee age.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_2</c> No end moment provided, but an end employee age is specified.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_3</c> End employee age must be zero in case of an AOW-based end moment.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_2</c> Result payroll component may not be a net-to-gross component.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_1</c> Calculation payroll period must be empty for automatic calculation and term calculation.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_2</c> If calculation payroll period is filled, payment payroll period must also be filled.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_1</c> Reference payroll period is only allowed when calculating via part-time.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_1</c> Advance payroll period may only be specified in combination with the payout payroll period.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_2</c> Payout at end of employment may only be specified in combination with the payout payroll period.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_3</c> Advance payroll period may not be equal to payout payroll period.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_4</c> Payout payroll period is only allowed with a cumulative base for calculation and with a term calculation via part-time.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_1</c> End of accrual payroll period is only allowed when calculating via part-time or cumulative calculation.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_2</c> End of accrual payroll period may not be the same as the calculation payroll period.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_IsPayoutAtStartOfEmployment_1</c> Advance payment upon employment only applies in case of extrapolation.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_IsPayoutAtEndOfEmployment_1</c> Payment upon termination of employment does not apply when calculating automatically or in a term calculation.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_1</c> Advance payroll component must not be filled in when calculating automatically.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_3</c> Advance percentage is filled in. Therefore, the advance payroll component must be known.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_4</c> Advance payroll component may not be a net-to-gross component.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePercentage_1</c> Advance percentage must be greater than 0.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_1</c> Advance payroll period is only allowed in combination with an advance.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_1</c> Periodic reservation payroll component must not be filled in when calculating automatically.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_3</c> Periodic reservation payroll component may not be a net-to-gross component.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_4</c> Periodic reservation component is only allowed with a cumulative base for calculation.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_FinancialReservationPercentage_1</c> Financial reservation percentage is not allowed with automatic calculation.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_1</c> Financial reservation percentage must be greater than 0 if the financial markup percentage is not 0.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_2</c> Financial markup percentage is not allowed with automatic calculation.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_IsPartTimeCalculation_1</c> Base for calculation is not a term calculation; part-time must be NO.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_1</c> Cumulative and automatic calculation may not both be set to YES.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_2</c> Calculation must be automatic for holiday accrual (component 182).
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_3</c> Calculation must be automatic for ADV accrual (component 184).
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_1</c> You have chosen a term calculation: not cumulative, not via part-time, and not automatic calculation. However, this type of calculation is no longer supported.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_AdvancePayrollComponent_Equal</c> Result payroll component is equal to advance payroll component. This is not allowed. 
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_PeriodicReservationPayrollComponent_Equal</c> Result payroll component is equal to periodic reservation payroll component. This is not allowed. 
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_PeriodicReservationPayrollComponent_Equal</c> Advance payroll component is equal to periodic reservation payroll component. This is not allowed.
    /// <br/>**Validation warnings**:
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Warning_1</c> Note! The payout payroll period lies in the NEXT year.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_1</c> Note! The accrual end payroll period lies in the NEXT year.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_2</c> Note! The base for calculation will be extrapolated.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_1</c> Note! The reference payroll period lies in the PREVIOUS year.
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_2</c> Note! The reference payroll period will be set to the calculation payroll period.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Patch))]
    [HttpPatch]
    [Route(BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "13dc22c6-9c9a-4cba-9700-a6b0a9d42872", // PatchBaseForCalculationByCollectiveLaborAgreementBaseForCalculationId
        "2bb7b769-7931-45d6-b9cb-0206d11f0f9c", // PatchBaseForCalculationByWageModelBaseForCalculationId
        "cd9ac747-a17f-4fd3-9a2b-d7cec0536abd")] // PatchBaseForCalculationByPayrollAdministrationBaseForCalculationId
    public async Task<ActionResult<DetailResult<BaseForCalculationModel>>> PatchBaseForCalculationByBaseForCalculationIdAsync(
        [FromRoute] Guid baseForCalculationId,
        [Required(ErrorMessage = "PATCH payload is required.")]
        BaseForCalculationPatchModel patchModel)
    {
        var result = await this.service.PatchBaseForCalculationByBaseForCalculationIdAsync(baseForCalculationId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Delete an existing base for calculation
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>DeleteBaseForCalculationByCollectiveLaborAgreementBaseForCalculationId</c>
    ///    - <c>DeleteBaseForCalculationByWageModelBaseForCalculationId</c>
    ///    - <c>DeleteBaseForCalculationByPayrollAdministrationBaseForCalculationId</c><br/>
    /// Deletes an existing base for calculation with the given id – defined on an inheritance level (CLA vs WM vs PA).
    /// <br/>**Validation errors**:
    ///    - <c>API_PayrollConfiguration_Delete_EntityHasChildren</c> Cannot delete entities from current inheritance level, because it has dependent child inheritance level(s).
    ///    - <c>API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted</c> Cannot delete data for the first payroll period in this year, because there is still data for later payroll period(s).
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_BasePayrollComponent</c> Base for calculation still has configuration: base payroll component
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMinimum</c> Base for calculation still has configuration: age-based minimum
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMaximum</c> Base for calculation still has configuration: age-based maximum
    ///    - <c>API_PayrollConfiguration_BaseForCalculation_Delete_InUse_EmploymentProfile</c> Base for calculation is used in: employment profile
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Delete))]
    [HttpDelete]
    [Route(BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "c8b89ae1-2c20-4ba3-a8cc-8537ec225c0d", // DeleteBaseForCalculationByCollectiveLaborAgreementBaseForCalculationId
        "f3ac347d-9e12-42b4-9a8b-105aac5d04a8", // DeleteBaseForCalculationByWageModelBaseForCalculationId
        "75ee2672-e2be-48de-b7d0-8f1bf754c2ef")] // DeleteBaseForCalculationByPayrollAdministrationBaseForCalculationId
    public async Task<ActionResult<DetailResult<NoResult>>> DeleteBaseForCalculationByBaseForCalculationIdAsync([FromRoute] Guid baseForCalculationId)
    {
        var result = await this.service.DeleteBaseForCalculationByBaseForCalculationIdAsync(baseForCalculationId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Metadata for base for calculation per provider
    /// </summary>
    /// <remarks>
    /// **Activity name**:
    ///    - <c>GetBaseForCalculationMetadataByProviderId</c><br/>
    /// Retrieves the list of available options for POST and PATCH.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(BaseForCalculationRoutes.GetBaseForCalculationMetadataByProviderIdAsync)]
    [AuthorizeEntity<ProviderAuthorizationModel>("eb8e28e1-523a-4ee5-91e0-69b826e6bbef")]
    public async Task<ActionResult<DetailResult<BaseForCalculationMetadataModel>>> GetBaseForCalculationMetadataByProviderIdAsync([FromRoute] Guid providerId)
    {
        var result = await this.service.GetBaseForCalculationMetadataByProviderIdAsync();
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// List of payroll components linked to a base for calculation
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetLinkedPayrollComponentsByCollectiveLaborAgreementBaseForCalculationId</c>
    ///    - <c>GetLinkedPayrollComponentsByWageModelBaseForCalculationId</c>
    ///    - <c>GetLinkedPayrollComponentsByPayrollAdministrationBaseForCalculationId</c><br/>
    /// Retrieves the list of linked payroll components for the given base for calculation – defined on an inheritance level (CLA vs WM vs PA).
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(BaseForCalculationRoutes.GetLinkedPayrollComponentsByBaseForCalculationId)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "79fa9837-7b1d-42bd-a00b-3377c3813a8c", // GetLinkedPayrollComponentsByCollectiveLaborAgreementBaseForCalculationId
        "6429f6fb-0d98-4079-a375-b07764adb597", // GetLinkedPayrollComponentsByWageModelBaseForCalculationId
        "92aeb9e0-63e8-4992-86c2-0e71ebe7d162")] // GetLinkedPayrollComponentsByPayrollAdministrationBaseForCalculationId
    public async Task<ActionResult<ListResult<PayrollComponentModel>>> GetLinkedPayrollComponentsByBaseForCalculationIdAsync(
        [FromRoute] Guid baseForCalculationId)
    {
        var result = await this.service.GetLinkedPayrollComponentsByBaseForCalculationIdAsync(baseForCalculationId);
        return this.resultHandler.ToTypedActionResult(result);
    }
}